!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n=n||self).ReduxSaga={})}(this,function(n){"use strict";var t=function(n){return"@@redux-saga/"+n},e=t("CANCEL_PROMISE"),r=t("CHANNEL_END"),o=t("IO"),a=t("MATCH"),c=t("MULTICAST"),u=t("SAGA_ACTION"),i=t("SELF_CANCELLATION"),f=t("TASK"),l=t("TASK_CANCEL"),s=t("TERMINATE"),v=t("LOCATION");function d(){return(d=Object.assign||function(n){for(var t=1;arguments.length>t;t++){var e=arguments[t];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r])}return n}).apply(this,arguments)}var p=function(n){return null==n},h=function(n){return null!=n},y=function(n){return"function"==typeof n},g=function(n){return"string"==typeof n},E=Array.isArray,m=function(n){return n&&y(n.then)},A=function(n){return n&&y(n.next)&&y(n.throw)},S=function n(t){return t&&(g(t)||b(t)||y(t)||E(t)&&t.every(n))},x=function(n){return n&&y(n.take)&&y(n.close)},C=function(n){return y(n)&&n.hasOwnProperty("toString")},b=function(n){return!!n&&"function"==typeof Symbol&&n.constructor===Symbol&&n!==Symbol.prototype},k=function(n){return x(n)&&n[c]},q=function(n){return function(){return n}}(!0),T=function(){},O=function(n){return n},L=function(n,t){d(n,t),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(t).forEach(function(e){n[e]=t[e]})},N=function(n,t){var e;return(e=[]).concat.apply(e,t.map(n))};function j(n,t){var e=n.indexOf(t);0>e||n.splice(e,1)}function w(n){var t=!1;return function(){t||(t=!0,n())}}var _=function(n){throw n},R=function(n){return{value:n,done:!0}};function M(n,t,e){void 0===t&&(t=_),void 0===e&&(e="iterator");var r={meta:{name:e},next:n,throw:t,return:R,isSagaIterator:!0};return"undefined"!=typeof Symbol&&(r[Symbol.iterator]=function(){return r}),r}function I(n,t){var e=t.sagaStack;console.error(n),console.error(e)}var U=function(n){return Array.apply(null,Array(n))},P=function(n){return function(t){return n(Object.defineProperty(t,u,{value:!0}))}},D=function(n){return n===s},H=function(n){return n===l},K=function(n){return D(n)||H(n)};function z(n,t){var e,r=Object.keys(n),o=r.length,a=0,c=E(n)?U(o):{},u={};return r.forEach(function(n){var r=function(r,u){e||(u||K(r)?(t.cancel(),t(r,u)):(c[n]=r,++a===o&&(e=!0,t(c))))};r.cancel=T,u[n]=r}),t.cancel=function(){e||(e=!0,r.forEach(function(n){return u[n].cancel()}))},u}function F(n){return{name:n.name||"anonymous",location:G(n)}}function G(n){return n[v]}var X="Channel's Buffer overflow!",J=1,B=3,Q=4,V={isEmpty:q,put:T,take:T};function W(n,t){void 0===n&&(n=10);var e=Array(n),r=0,o=0,a=0,c=function(t){e[o]=t,o=(o+1)%n,r++},u=function(){if(0!=r){var t=e[a];return e[a]=null,r--,a=(a+1)%n,t}},i=function(){for(var n=[];r;)n.push(u());return n};return{isEmpty:function(){return 0==r},put:function(u){var f;if(n>r)c(u);else switch(t){case J:throw Error(X);case B:e[o]=u,a=o=(o+1)%n;break;case Q:f=2*n,e=i(),r=e.length,o=e.length,a=0,e.length=f,n=f,c(u)}},take:u,flush:i}}var Y=function(){return V},Z=function(n){return W(n,B)},$=function(n){return W(n,Q)},nn=Object.freeze({__proto__:null,none:Y,fixed:function(n){return W(n,J)},dropping:function(n){return W(n,2)},sliding:Z,expanding:$}),tn=[],en=0;function rn(n){try{cn(),n()}finally{un()}}function on(n){tn.push(n),en||(cn(),fn())}function an(n){try{return cn(),n()}finally{fn()}}function cn(){en++}function un(){en--}function fn(){var n;for(un();!en&&void 0!==(n=tn.shift());)rn(n)}var ln=function(n){return function(t){return n.some(function(n){return hn(n)(t)})}},sn=function(n){return function(t){return n(t)}},vn=function(n){return function(t){return t.type===n+""}},dn=function(n){return function(t){return t.type===n}},pn=function(){return q};function hn(n){var t="*"===n?pn:g(n)?vn:E(n)?ln:C(n)?vn:y(n)?sn:b(n)?dn:null;if(null===t)throw Error("invalid pattern: "+n);return t(n)}var yn={type:r},gn=function(n){return n&&n.type===r};function En(n){void 0===n&&(n=$());var t=!1,e=[];return{take:function(r){t&&n.isEmpty()?r(yn):n.isEmpty()?(e.push(r),r.cancel=function(){j(e,r)}):r(n.take())},put:function(r){if(!t){if(0===e.length)return n.put(r);e.shift()(r)}},flush:function(e){t&&n.isEmpty()?e(yn):e(n.flush())},close:function(){if(!t){t=!0;var n=e;e=[];for(var r=0,o=n.length;o>r;r++)(0,n[r])(yn)}}}}function mn(){var n,t=!1,e=[],r=e,o=function(){r===e&&(r=e.slice())},u=function(){t=!0;var n=e=r;r=[],n.forEach(function(n){n(yn)})};return(n={})[c]=!0,n.put=function(n){if(!t)if(gn(n))u();else for(var o=e=r,c=0,i=o.length;i>c;c++){var f=o[c];f[a](n)&&(f.cancel(),f(n))}},n.take=function(n,e){void 0===e&&(e=pn),t?n(yn):(n[a]=e,o(),r.push(n),n.cancel=w(function(){o(),j(r,n)}))},n.close=u,n}function An(){var n=mn(),t=n.put;return n.put=function(n){n[u]?t(n):on(function(){t(n)})},n}var Sn=0,xn=1,Cn=2,bn=3,kn="TAKE",qn="PUT",Tn="ALL",On="RACE",Ln="CALL",Nn="CPS",jn="FORK",wn="JOIN",_n="CANCEL",Rn="SELECT",Mn="ACTION_CHANNEL",In="CANCELLED",Un="FLUSH",Pn="GET_CONTEXT",Dn="SET_CONTEXT",Hn=Object.freeze({__proto__:null,TAKE:kn,PUT:qn,ALL:Tn,RACE:On,CALL:Ln,CPS:Nn,FORK:jn,JOIN:wn,CANCEL:_n,SELECT:Rn,ACTION_CHANNEL:Mn,CANCELLED:In,FLUSH:Un,GET_CONTEXT:Pn,SET_CONTEXT:Dn});function Kn(n,t){var r=n[e];y(r)&&(t.cancel=r),n.then(t,function(n){t(n,!0)})}var zn,Fn=0,Gn=function(){return++Fn};function Xn(n){n.isRunning()&&n.cancel()}var Jn=((zn={})[kn]=function(n,t,e){var r=t.channel,o=void 0===r?n.channel:r,a=t.pattern,c=t.maybe,u=function(n){n instanceof Error?e(n,!0):!gn(n)||c?e(n):e(s)};try{o.take(u,h(a)?hn(a):null)}catch(n){return void e(n,!0)}e.cancel=u.cancel},zn[qn]=function(n,t,e){var r=t.channel,o=t.action,a=t.resolve;on(function(){var t;try{t=(r?r.put:n.dispatch)(o)}catch(n){return void e(n,!0)}a&&m(t)?Kn(t,e):e(t)})},zn[Tn]=function(n,t,e,r){var o=r.digestEffect,a=Fn,c=Object.keys(t);if(0!==c.length){var u=z(t,e);c.forEach(function(n){o(t[n],a,u[n],n)})}else e(E(t)?[]:{})},zn[On]=function(n,t,e,r){var o=r.digestEffect,a=Fn,c=Object.keys(t),u=E(t)?U(c.length):{},i={},f=!1;c.forEach(function(n){var t=function(t,r){f||(r||K(t)?(e.cancel(),e(t,r)):(e.cancel(),f=!0,u[n]=t,e(u)))};t.cancel=T,i[n]=t}),e.cancel=function(){f||(f=!0,c.forEach(function(n){return i[n].cancel()}))},c.forEach(function(n){f||o(t[n],a,i[n],n)})},zn[Ln]=function(n,t,e,r){var o=t.context,a=t.fn,c=t.args,u=r.task;try{var i=a.apply(o,c);if(m(i))return void Kn(i,e);if(A(i))return void rt(n,i,u.context,Fn,F(a),!1,e);e(i)}catch(n){e(n,!0)}},zn[Nn]=function(n,t,e){var r=t.context,o=t.fn,a=t.args;try{var c=function(n,t){p(n)?e(t):e(n,!0)};o.apply(r,a.concat(c)),c.cancel&&(e.cancel=c.cancel)}catch(n){e(n,!0)}},zn[jn]=function(n,t,e,r){var o=t.fn,a=t.detached,c=r.task,u=function(n){var t=n.context,e=n.fn,r=n.args;try{var o=e.apply(t,r);if(A(o))return o;var a=!1;return M(function(n){return a?{value:n,done:!0}:(a=!0,{value:o,done:!m(o)})})}catch(n){return M(function(){throw n})}}({context:t.context,fn:o,args:t.args}),i=function(n,t){return n.isSagaIterator?{name:n.meta.name}:F(t)}(u,o);an(function(){var t=rt(n,u,c.context,Fn,i,a,void 0);a?e(t):t.isRunning()?(c.queue.addTask(t),e(t)):t.isAborted()?c.queue.abort(t.error()):e(t)})},zn[wn]=function(n,t,e,r){var o=r.task,a=function(n,t){if(n.isRunning()){var e={task:o,cb:t};t.cancel=function(){n.isRunning()&&j(n.joiners,e)},n.joiners.push(e)}else n.isAborted()?t(n.error(),!0):t(n.result())};if(E(t)){if(0===t.length)return void e([]);var c=z(t,e);t.forEach(function(n,t){a(n,c[t])})}else a(t,e)},zn[_n]=function(n,t,e,r){t===i?Xn(r.task):E(t)?t.forEach(Xn):Xn(t),e()},zn[Rn]=function(n,t,e){var r=t.selector,o=t.args;try{e(r.apply(void 0,[n.getState()].concat(o)))}catch(n){e(n,!0)}},zn[Mn]=function(n,t,e){var r=t.pattern,o=En(t.buffer),a=hn(r),c=function t(e){gn(e)||n.channel.take(t,a),o.put(e)},u=o.close;o.close=function(){c.cancel(),u()},n.channel.take(c,a),e(o)},zn[In]=function(n,t,e,r){e(r.task.isCancelled())},zn[Un]=function(n,t,e){t.flush(e)},zn[Pn]=function(n,t,e,r){e(r.task.context[t])},zn[Dn]=function(n,t,e,r){L(r.task.context,t),e()},zn);function Bn(n,t){return n+"?"+t}function Qn(n){var t=n.name,e=n.location;return e?t+"  "+Bn(e.fileName,e.lineNumber):t}function Vn(n){var t=N(function(n){return n.cancelledTasks},n);return t.length?["Tasks cancelled due to error:"].concat(t).join("\n"):""}var Wn=null,Yn=[],Zn=function(n){n.crashedEffect=Wn,Yn.push(n)},$n=function(){Wn=null,Yn.length=0},nt=function(n){Wn=n},tt=function(){var n,t=Yn[0],e=Yn.slice(1),r=t.crashedEffect?(n=G(t.crashedEffect))?n.code+"  "+Bn(n.fileName,n.lineNumber):"":null;return["The above error occurred in task "+Qn(t.meta)+(r?" \n when executing effect "+r:"")].concat(e.map(function(n){return"    created by "+Qn(n.meta)}),[Vn(Yn)]).join("\n")};function et(n,t,e,r,o,a,c){var u;void 0===c&&(c=T);var i,s,v=Sn,d=null,p=[],h=Object.create(e),y=function(n,t,e){var r,o=[],a=!1;function c(n){t(),i(),e(n,!0)}function u(t){o.push(t),t.cont=function(u,i){a||(j(o,t),t.cont=T,i?c(u):(t===n&&(r=u),o.length||(a=!0,e(r))))}}function i(){a||(a=!0,o.forEach(function(n){n.cont=T,n.cancel()}),o=[])}return u(n),{addTask:u,cancelAll:i,abort:c,getTasks:function(){return o}}}(t,function(){p.push.apply(p,y.getTasks().map(function(n){return n.meta.name}))},g);function g(t,e){if(e){if(v=Cn,Zn({meta:o,cancelledTasks:p}),E.isRoot){var r=tt();$n(),n.onError(t,{sagaStack:r})}s=t,d&&d.reject(t)}else t===l?v=xn:v!==xn&&(v=bn),i=t,d&&d.resolve(t);E.cont(t,e),E.joiners.forEach(function(n){n.cb(t,e)}),E.joiners=null}var E=((u={})[f]=!0,u.id=r,u.meta=o,u.isRoot=a,u.context=h,u.joiners=[],u.queue=y,u.cancel=function(){v===Sn&&(v=xn,y.cancelAll(),g(l,!1))},u.cont=c,u.end=g,u.setContext=function(n){L(h,n)},u.toPromise=function(){return d?d.promise:((n={}).promise=new Promise(function(t,e){n.resolve=t,n.reject=e}),d=n,v===Cn?d.reject(s):v!==Sn&&d.resolve(i),d.promise);var n},u.isRunning=function(){return v===Sn},u.isCancelled=function(){return v===xn||v===Sn&&t.status===xn},u.isAborted=function(){return v===Cn},u.result=function(){return i},u.error=function(){return s},u);return E}function rt(n,t,e,r,a,c,u){var i=n.finalizeRunEffect(function(t,e,r){if(m(t))Kn(t,r);else if(A(t))rt(n,t,s.context,e,a,!1,r);else if(t&&t[o]){var c=Jn[t.type];c(n,t.payload,r,v)}else r(t)});d.cancel=T;var f={meta:a,cancel:function(){f.status===Sn&&(f.status=xn,d(l))},status:Sn},s=et(n,f,e,r,a,c,u),v={task:s,digestEffect:p};return u&&(u.cancel=s.cancel),d(),s;function d(n,e){try{var o;e?(o=t.throw(n),$n()):H(n)?(f.status=xn,d.cancel(),o=y(t.return)?t.return(l):{done:!0,value:l}):o=D(n)?y(t.return)?t.return():{done:!0}:t.next(n),o.done?(f.status!==xn&&(f.status=bn),f.cont(o.value)):p(o.value,r,d)}catch(n){if(f.status===xn)throw n;f.status=Cn,f.cont(n,!0)}}function p(t,e,r,o){void 0===o&&(o="");var a,c=Gn();function u(e,o){a||(a=!0,r.cancel=T,n.sagaMonitor&&(o?n.sagaMonitor.effectRejected(c,e):n.sagaMonitor.effectResolved(c,e)),o&&nt(t),r(e,o))}n.sagaMonitor&&n.sagaMonitor.effectTriggered({effectId:c,parentEffectId:e,label:o,effect:t}),u.cancel=T,r.cancel=function(){a||(a=!0,u.cancel(),u.cancel=T,n.sagaMonitor&&n.sagaMonitor.effectCancelled(c))},i(t,c,u)}}function ot(n,t){for(var e=n.channel,r=void 0===e?An():e,o=n.dispatch,a=n.getState,c=n.context,u=void 0===c?{}:c,i=n.sagaMonitor,f=n.effectMiddlewares,l=n.onError,s=void 0===l?I:l,v=arguments.length,d=Array(v>2?v-2:0),p=2;v>p;p++)d[p-2]=arguments[p];var h,y=t.apply(void 0,d),g=Gn();if(i&&(i.rootSagaStarted=i.rootSagaStarted||T,i.effectTriggered=i.effectTriggered||T,i.effectResolved=i.effectResolved||T,i.effectRejected=i.effectRejected||T,i.effectCancelled=i.effectCancelled||T,i.actionDispatched=i.actionDispatched||T,i.rootSagaStarted({effectId:g,saga:t,args:d})),f){var E=function(){for(var n=arguments.length,t=Array(n),e=0;n>e;e++)t[e]=arguments[e];return 0===t.length?function(n){return n}:1===t.length?t[0]:t.reduce(function(n,t){return function(){return n(t.apply(void 0,arguments))}})}.apply(void 0,f);h=function(n){return function(t,e,r){return E(function(t){return n(t,e,r)})(t)}}}else h=O;var m={channel:r,dispatch:P(o),getState:a,sagaMonitor:i,onError:s,finalizeRunEffect:h};return an(function(){var n=rt(m,y,u,g,F(t),!0,void 0);return i&&i.effectResolved(g,n),n})}var at=2147483647;function ct(n,t){var r;void 0===t&&(t=!0);var o=new Promise(function(e){r=setTimeout(e,Math.min(at,n),t)});return o[e]=function(){clearTimeout(r)},o}var ut=function(n,t){var e;return(e={})[o]=!0,e.combinator=!1,e.type=n,e.payload=t,e},it=function(n){return ut(jn,d({},n.payload,{detached:!0}))};function ft(n,t){return void 0===n&&(n="*"),S(n)?(h(t)&&console.warn("take(pattern) takes one argument but two were provided. Consider passing an array for listening to several action types"),ut(kn,{pattern:n})):k(n)&&h(t)&&S(t)?ut(kn,{channel:n,pattern:t}):x(n)?(h(t)&&console.warn("take(channel) takes one argument but two were provided. Second argument is ignored."),ut(kn,{channel:n})):void 0}function lt(n,t){return p(t)&&(t=n,n=void 0),ut(qn,{channel:n,action:t})}function st(n){var t=ut(On,n);return t.combinator=!0,t}function vt(n,t){var e,r=null;return y(n)?e=n:(E(n)?(r=n[0],e=n[1]):(r=n.context,e=n.fn),r&&g(e)&&y(r[e])&&(e=r[e])),{context:r,fn:e,args:t}}function dt(n){for(var t=arguments.length,e=Array(t>1?t-1:0),r=1;t>r;r++)e[r-1]=arguments[r];return ut(Ln,vt(n,e))}function pt(n){for(var t=arguments.length,e=Array(t>1?t-1:0),r=1;t>r;r++)e[r-1]=arguments[r];return ut(jn,vt(n,e))}function ht(n){return void 0===n&&(n=i),ut(_n,n)}function yt(n,t){return ut(Mn,{pattern:n,buffer:t})}var gt=dt.bind(null,ct),Et=function(n){return{done:!0,value:n}},mt={};function At(n){return x(n)?"channel":C(n)?n+"":y(n)?n.name:n+""}function St(n,t,e){var r,o,a,c=t;function u(t,e){if(c===mt)return Et(t);if(e&&!o)throw c=mt,e;r&&r(t);var u=e?n[o](e):n[c]();return a=u.effect,r=u.stateUpdater,o=u.errorState,(c=u.nextState)===mt?Et(t):a}return M(u,function(n){return u(null,n)},e)}function xt(n,t){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];var a,c={done:!1,value:ft(n)},u=function(n){return a=n};return St({q1:function(){return{nextState:"q2",effect:c,stateUpdater:u}},q2:function(){return{nextState:"q1",effect:(n=a,{done:!1,value:pt.apply(void 0,[t].concat(r,[n]))})};var n}},"q1","takeEvery("+At(n)+", "+t.name+")")}function Ct(n,t){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];var a,c,u={done:!1,value:ft(n)},i=function(n){return{done:!1,value:pt.apply(void 0,[t].concat(r,[n]))}},f=function(n){return{done:!1,value:ht(n)}},l=function(n){return a=n},s=function(n){return c=n};return St({q1:function(){return{nextState:"q2",effect:u,stateUpdater:s}},q2:function(){return a?{nextState:"q3",effect:f(a)}:{nextState:"q1",effect:i(c),stateUpdater:l}},q3:function(){return{nextState:"q1",effect:i(c),stateUpdater:l}}},"q1","takeLatest("+At(n)+", "+t.name+")")}function bt(n,t){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];var a,c={done:!1,value:ft(n)},u=function(n){return a=n};return St({q1:function(){return{nextState:"q2",effect:c,stateUpdater:u}},q2:function(){return{nextState:"q1",effect:(n=a,{done:!1,value:dt.apply(void 0,[t].concat(r,[n]))})};var n}},"q1","takeLeading("+At(n)+", "+t.name+")")}function kt(n,t,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];var c,u,i={done:!1,value:gt(n)},f=function(n){return c=n},l=function(n){return u=n},s=!x(t);return s||l(t),St({q1:function(){return{nextState:"q2",effect:{done:!1,value:yt(t,Z(1))},stateUpdater:l}},q2:function(){return{nextState:"q3",effect:{done:!1,value:ft(u)},stateUpdater:f}},q3:function(){return{nextState:"q4",effect:(n=c,{done:!1,value:pt.apply(void 0,[e].concat(o,[n]))})};var n},q4:function(){return{nextState:"q2",effect:i}}},s?"q1":"q2","throttle("+At(t)+", "+e.name+")")}function qt(n,t,e){for(var r=n,o=arguments.length,a=Array(o>3?o-3:0),c=3;o>c;c++)a[c-3]=arguments[c];var u={done:!1,value:dt.apply(void 0,[e].concat(a))},i={done:!1,value:gt(t)};return St({q1:function(){return{nextState:"q2",effect:u,errorState:"q10"}},q2:function(){return{nextState:mt}},q10:function(n){if(0>=(r-=1))throw n;return{nextState:"q1",effect:i}}},"q1","retry("+e.name+")")}function Tt(n,t,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];var c,u,i={done:!1,value:ft(t)},f={done:!1,value:st({action:ft(t),debounce:gt(n)})},l=function(n){return c=n},s=function(n){return u=n};return St({q1:function(){return{nextState:"q2",effect:i,stateUpdater:l}},q2:function(){return{nextState:"q3",effect:f,stateUpdater:s}},q3:function(){return u.debounce?{nextState:"q1",effect:(t=c,{done:!1,value:pt.apply(void 0,[e].concat(o,[t]))})}:{nextState:"q2",effect:(n=u.action,{done:!1,value:n}),stateUpdater:l};var n,t}},"q1","debounce("+At(t)+", "+e.name+")")}var Ot=Object.freeze({__proto__:null,effectTypes:Hn,take:ft,takeMaybe:function(){var n=ft.apply(void 0,arguments);return n.payload.maybe=!0,n},put:lt,putResolve:function(){var n=lt.apply(void 0,arguments);return n.payload.resolve=!0,n},all:function(n){var t=ut(Tn,n);return t.combinator=!0,t},race:st,call:dt,apply:function(n,t,e){return void 0===e&&(e=[]),ut(Ln,vt([n,t],e))},cps:function(n){for(var t=arguments.length,e=Array(t>1?t-1:0),r=1;t>r;r++)e[r-1]=arguments[r];return ut(Nn,vt(n,e))},fork:pt,spawn:function(n){for(var t=arguments.length,e=Array(t>1?t-1:0),r=1;t>r;r++)e[r-1]=arguments[r];return it(pt.apply(void 0,[n].concat(e)))},join:function(n){return ut(wn,n)},cancel:ht,select:function(n){void 0===n&&(n=O);for(var t=arguments.length,e=Array(t>1?t-1:0),r=1;t>r;r++)e[r-1]=arguments[r];return ut(Rn,{selector:n,args:e})},actionChannel:yt,cancelled:function(){return ut(In,{})},flush:function(n){return ut(Un,n)},getContext:function(n){return ut(Pn,n)},setContext:function(n){return ut(Dn,n)},delay:gt,debounce:function(n,t,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];return pt.apply(void 0,[Tt,n,t,e].concat(o))},retry:function(n,t,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];return dt.apply(void 0,[qt,n,t,e].concat(o))},takeEvery:function(n,t){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];return pt.apply(void 0,[xt,n,t].concat(r))},takeLatest:function(n,t){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];return pt.apply(void 0,[Ct,n,t].concat(r))},takeLeading:function(n,t){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];return pt.apply(void 0,[bt,n,t].concat(r))},throttle:function(n,t,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];return pt.apply(void 0,[kt,n,t,e].concat(o))}});n.CANCEL=e,n.END=yn,n.SAGA_LOCATION=v,n.buffers=nn,n.channel=En,n.default=function(n){var t,e=void 0===n?{}:n,r=e.context,o=void 0===r?{}:r,a=e.channel,c=void 0===a?An():a,u=e.sagaMonitor,i=function(n,t){if(null==n)return{};var e,r,o={},a=Object.keys(n);for(r=0;a.length>r;r++)0>t.indexOf(e=a[r])&&(o[e]=n[e]);return o}(e,["context","channel","sagaMonitor"]);function f(n){return t=ot.bind(null,d({},i,{context:o,channel:c,dispatch:n.dispatch,getState:n.getState,sagaMonitor:u})),function(n){return function(t){u&&u.actionDispatched&&u.actionDispatched(t);var e=n(t);return c.put(t),e}}}return f.run=function(){return t.apply(void 0,arguments)},f.setContext=function(n){L(o,n)},f},n.detach=it,n.effects=Ot,n.eventChannel=function(n,t){void 0===t&&(t=Y());var e,r=!1,o=En(t),a=function(){r||(r=!0,y(e)&&e(),o.close())};return e=w(e=n(function(n){gn(n)?a():o.put(n)})),r&&e(),{take:o.take,flush:o.flush,close:a}},n.isEnd=gn,n.multicastChannel=mn,n.runSaga=ot,n.stdChannel=An,Object.defineProperty(n,"__esModule",{value:!0})});
