!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t=t||self).ReduxSagaEffects={})}(this,function(t){"use strict";function n(){return(n=Object.assign||function(t){for(var n=1;arguments.length>n;n++){var e=arguments[n];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])}return t}).apply(this,arguments)}var e=function(t){return"@@redux-saga/"+t},r=e("CANCEL_PROMISE"),o=e("IO"),a=e("MULTICAST"),u=e("SELF_CANCELLATION"),c=2147483647;function f(t,n){var e;void 0===n&&(n=!0);var o=new Promise(function(r){e=setTimeout(r,Math.min(c,t),n)});return o[r]=function(){clearTimeout(e)},o}var i=function(t){return null==t},l=function(t){return null!=t},v=function(t){return"function"==typeof t},d=function(t){return"string"==typeof t},p=Array.isArray,y=function t(n){return n&&(d(n)||q(n)||v(n)||p(n)&&n.every(t))},s=function(t){return t&&v(t.take)&&v(t.close)},h=function(t){return v(t)&&t.hasOwnProperty("toString")},q=function(t){return!!t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype},S=function(t){return s(t)&&t[a]},A=function(t){return t},g=function(t){throw t},C=function(t){return{value:t,done:!0}};var E="TAKE",x="PUT",L="ALL",m="RACE",T="CALL",b="CPS",N="FORK",O="JOIN",w="CANCEL",k="SELECT",U="ACTION_CHANNEL",_="CANCELLED",I="FLUSH",P="GET_CONTEXT",R="SET_CONTEXT",j=Object.freeze({__proto__:null,TAKE:E,PUT:x,ALL:L,RACE:m,CALL:T,CPS:b,FORK:N,JOIN:O,CANCEL:w,SELECT:k,ACTION_CHANNEL:U,CANCELLED:_,FLUSH:I,GET_CONTEXT:P,SET_CONTEXT:R}),F=function(t,n){var e;return(e={})[o]=!0,e.combinator=!1,e.type=t,e.payload=n,e},M=function(t){return F(N,n({},t.payload,{detached:!0}))};function H(t,n){return void 0===t&&(t="*"),y(t)?(l(n)&&console.warn("take(pattern) takes one argument but two were provided. Consider passing an array for listening to several action types"),F(E,{pattern:t})):S(t)&&l(n)&&y(n)?F(E,{channel:t,pattern:n}):s(t)?(l(n)&&console.warn("take(channel) takes one argument but two were provided. Second argument is ignored."),F(E,{channel:t})):void 0}function K(t,n){return i(n)&&(n=t,t=void 0),F(x,{channel:t,action:n})}function X(t){var n=F(m,t);return n.combinator=!0,n}function D(t,n){var e,r=null;return v(t)?e=t:(p(t)?(r=t[0],e=t[1]):(r=t.context,e=t.fn),r&&d(e)&&v(r[e])&&(e=r[e])),{context:r,fn:e,args:n}}function G(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;n>r;r++)e[r-1]=arguments[r];return F(T,D(t,e))}function J(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;n>r;r++)e[r-1]=arguments[r];return F(N,D(t,e))}function z(t){return void 0===t&&(t=u),F(w,t)}function B(t,n){return F(U,{pattern:t,buffer:n})}var Q=G.bind(null,f),V=function(t){return{done:!0,value:t}},W={};function Y(t){return s(t)?"channel":h(t)?t+"":v(t)?t.name:t+""}function Z(t,n,e){var r,o,a,u=n;function c(n,e){if(u===W)return V(n);if(e&&!o)throw u=W,e;r&&r(n);var c=e?t[o](e):t[u]();return a=c.effect,r=c.stateUpdater,o=c.errorState,(u=c.nextState)===W?V(n):a}return function(t,n,e){void 0===n&&(n=g),void 0===e&&(e="iterator");var r={meta:{name:e},next:t,throw:n,return:C,isSagaIterator:!0};return"undefined"!=typeof Symbol&&(r[Symbol.iterator]=function(){return r}),r}(c,function(t){return c(null,t)},e)}function $(t,n){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];var a,u={done:!1,value:H(t)},c=function(t){return a=t};return Z({q1:function(){return{nextState:"q2",effect:u,stateUpdater:c}},q2:function(){return{nextState:"q1",effect:(t=a,{done:!1,value:J.apply(void 0,[n].concat(r,[t]))})};var t}},"q1","takeEvery("+Y(t)+", "+n.name+")")}function tt(t,n){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];var a,u,c={done:!1,value:H(t)},f=function(t){return{done:!1,value:J.apply(void 0,[n].concat(r,[t]))}},i=function(t){return{done:!1,value:z(t)}},l=function(t){return a=t},v=function(t){return u=t};return Z({q1:function(){return{nextState:"q2",effect:c,stateUpdater:v}},q2:function(){return a?{nextState:"q3",effect:i(a)}:{nextState:"q1",effect:f(u),stateUpdater:l}},q3:function(){return{nextState:"q1",effect:f(u),stateUpdater:l}}},"q1","takeLatest("+Y(t)+", "+n.name+")")}function nt(t,n){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];var a,u={done:!1,value:H(t)},c=function(t){return a=t};return Z({q1:function(){return{nextState:"q2",effect:u,stateUpdater:c}},q2:function(){return{nextState:"q1",effect:(t=a,{done:!1,value:G.apply(void 0,[n].concat(r,[t]))})};var t}},"q1","takeLeading("+Y(t)+", "+n.name+")")}var et="Channel's Buffer overflow!",rt=1,ot=3,at=4;var ut=function(t){return function(t,n){void 0===t&&(t=10);var e=Array(t),r=0,o=0,a=0,u=function(n){e[o]=n,o=(o+1)%t,r++},c=function(){if(0!=r){var n=e[a];return e[a]=null,r--,a=(a+1)%t,n}},f=function(){for(var t=[];r;)t.push(c());return t};return{isEmpty:function(){return 0==r},put:function(c){var i;if(t>r)u(c);else switch(n){case rt:throw Error(et);case ot:e[o]=c,a=o=(o+1)%t;break;case at:i=2*t,e=f(),r=e.length,o=e.length,a=0,e.length=i,t=i,u(c)}},take:c,flush:f}}(t,ot)};function ct(t,n,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];var u,c,f={done:!1,value:Q(t)},i=function(t){return u=t},l=function(t){return c=t},v=!s(n);return v||l(n),Z({q1:function(){return{nextState:"q2",effect:{done:!1,value:B(n,ut(1))},stateUpdater:l}},q2:function(){return{nextState:"q3",effect:{done:!1,value:H(c)},stateUpdater:i}},q3:function(){return{nextState:"q4",effect:(t=u,{done:!1,value:J.apply(void 0,[e].concat(o,[t]))})};var t},q4:function(){return{nextState:"q2",effect:f}}},v?"q1":"q2","throttle("+Y(n)+", "+e.name+")")}function ft(t,n,e){for(var r=t,o=arguments.length,a=Array(o>3?o-3:0),u=3;o>u;u++)a[u-3]=arguments[u];var c={done:!1,value:G.apply(void 0,[e].concat(a))},f={done:!1,value:Q(n)};return Z({q1:function(){return{nextState:"q2",effect:c,errorState:"q10"}},q2:function(){return{nextState:W}},q10:function(t){if(0>=(r-=1))throw t;return{nextState:"q1",effect:f}}},"q1","retry("+e.name+")")}function it(t,n,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];var u,c,f={done:!1,value:H(n)},i={done:!1,value:X({action:H(n),debounce:Q(t)})},l=function(t){return u=t},v=function(t){return c=t};return Z({q1:function(){return{nextState:"q2",effect:f,stateUpdater:l}},q2:function(){return{nextState:"q3",effect:i,stateUpdater:v}},q3:function(){return c.debounce?{nextState:"q1",effect:(n=u,{done:!1,value:J.apply(void 0,[e].concat(o,[n]))})}:{nextState:"q2",effect:(t=c.action,{done:!1,value:t}),stateUpdater:l};var t,n}},"q1","debounce("+Y(n)+", "+e.name+")")}t.actionChannel=B,t.all=function(t){var n=F(L,t);return n.combinator=!0,n},t.apply=function(t,n,e){return void 0===e&&(e=[]),F(T,D([t,n],e))},t.call=G,t.cancel=z,t.cancelled=function(){return F(_,{})},t.cps=function(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;n>r;r++)e[r-1]=arguments[r];return F(b,D(t,e))},t.debounce=function(t,n,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];return J.apply(void 0,[it,t,n,e].concat(o))},t.delay=Q,t.effectTypes=j,t.flush=function(t){return F(I,t)},t.fork=J,t.getContext=function(t){return F(P,t)},t.join=function(t){return F(O,t)},t.put=K,t.putResolve=function(){var t=K.apply(void 0,arguments);return t.payload.resolve=!0,t},t.race=X,t.retry=function(t,n,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];return G.apply(void 0,[ft,t,n,e].concat(o))},t.select=function(t){void 0===t&&(t=A);for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;n>r;r++)e[r-1]=arguments[r];return F(k,{selector:t,args:e})},t.setContext=function(t){return F(R,t)},t.spawn=function(t){for(var n=arguments.length,e=Array(n>1?n-1:0),r=1;n>r;r++)e[r-1]=arguments[r];return M(J.apply(void 0,[t].concat(e)))},t.take=H,t.takeEvery=function(t,n){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];return J.apply(void 0,[$,t,n].concat(r))},t.takeLatest=function(t,n){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];return J.apply(void 0,[tt,t,n].concat(r))},t.takeLeading=function(t,n){for(var e=arguments.length,r=Array(e>2?e-2:0),o=2;e>o;o++)r[o-2]=arguments[o];return J.apply(void 0,[nt,t,n].concat(r))},t.takeMaybe=function(){var t=H.apply(void 0,arguments);return t.payload.maybe=!0,t},t.throttle=function(t,n,e){for(var r=arguments.length,o=Array(r>3?r-3:0),a=3;r>a;a++)o[a-3]=arguments[a];return J.apply(void 0,[ct,t,n,e].concat(o))},Object.defineProperty(t,"__esModule",{value:!0})});
