import{H as e,a4 as a,b as t,f as r,a5 as n,a6 as f,a7 as o,a8 as s,a9 as u,aa as c,ab as l,ac as q}from"./io-1d900520.js";export{aa as actionChannel,ah as all,ai as apply,a8 as call,a7 as cancel,an as cancelled,aj as cps,a9 as delay,ad as effectTypes,ao as flush,a6 as fork,ap as getContext,al as join,af as put,ag as putResolve,ac as race,am as select,aq as setContext,ak as spawn,a5 as take,ae as takeMaybe}from"./io-1d900520.js";var v=e=>({done:!0,value:e}),d={};function i(e){return a(e)?"channel":t(e)?e+"":r(e)?e.name:e+""}function x(a,t,r){var n,f,o,s=t;function u(e,t){if(s===d)return v(e);if(t&&!f)throw s=d,t;n&&n(e);var r=t?a[f](t):a[s]();return({nextState:s,effect:o,stateUpdater:n,errorState:f}=r),s===d?v(e):o}return e(u,e=>u(null,e),r)}function p(e,a){for(var t=arguments.length,r=Array(t>2?t-2:0),o=2;t>o;o++)r[o-2]=arguments[o];var s,u={done:!1,value:n(e)},c=e=>({done:!1,value:f(a,...r,e)}),l=e=>s=e;return x({q1:()=>({nextState:"q2",effect:u,stateUpdater:l}),q2:()=>({nextState:"q1",effect:c(s)})},"q1","takeEvery("+i(e)+", "+a.name+")")}function S(e,a){for(var t=arguments.length,r=Array(t>2?t-2:0),s=2;t>s;s++)r[s-2]=arguments[s];var u,c,l={done:!1,value:n(e)},q=e=>({done:!1,value:f(a,...r,e)}),v=e=>({done:!1,value:o(e)}),d=e=>u=e,p=e=>c=e;return x({q1:()=>({nextState:"q2",effect:l,stateUpdater:p}),q2:()=>u?{nextState:"q3",effect:v(u)}:{nextState:"q1",effect:q(c),stateUpdater:d},q3:()=>({nextState:"q1",effect:q(c),stateUpdater:d})},"q1","takeLatest("+i(e)+", "+a.name+")")}function h(e,a){for(var t=arguments.length,r=Array(t>2?t-2:0),f=2;t>f;f++)r[f-2]=arguments[f];var o,u={done:!1,value:n(e)},c=e=>({done:!1,value:s(a,...r,e)}),l=e=>o=e;return x({q1:()=>({nextState:"q2",effect:u,stateUpdater:l}),q2:()=>({nextState:"q1",effect:c(o)})},"q1","takeLeading("+i(e)+", "+a.name+")")}function y(e,t,r){for(var o=arguments.length,s=Array(o>3?o-3:0),q=3;o>q;q++)s[q-3]=arguments[q];var v,d,p=()=>({done:!1,value:n(d)}),S=e=>({done:!1,value:f(r,...s,e)}),h={done:!1,value:u(e)},y=e=>v=e,g=e=>d=e,A=!a(t);return A||g(t),x({q1:()=>({nextState:"q2",effect:{done:!1,value:c(t,l(1))},stateUpdater:g}),q2:()=>({nextState:"q3",effect:p(),stateUpdater:y}),q3:()=>({nextState:"q4",effect:S(v)}),q4:()=>({nextState:"q2",effect:h})},A?"q1":"q2","throttle("+i(t)+", "+r.name+")")}function g(e,a,t){for(var r=e,n=arguments.length,f=Array(n>3?n-3:0),o=3;n>o;o++)f[o-3]=arguments[o];var c={done:!1,value:s(t,...f)},l={done:!1,value:u(a)};return x({q1:()=>({nextState:"q2",effect:c,errorState:"q10"}),q2:()=>({nextState:d}),q10(e){if(0>=(r-=1))throw e;return{nextState:"q1",effect:l}}},"q1","retry("+t.name+")")}function A(e,a,t){for(var r=arguments.length,o=Array(r>3?r-3:0),s=3;r>s;s++)o[s-3]=arguments[s];var c,l,v={done:!1,value:n(a)},d={done:!1,value:q({action:n(a),debounce:u(e)})},p=e=>({done:!1,value:f(t,...o,e)}),S=e=>({done:!1,value:e}),h=e=>c=e,y=e=>l=e;return x({q1:()=>({nextState:"q2",effect:v,stateUpdater:h}),q2:()=>({nextState:"q3",effect:d,stateUpdater:y}),q3:()=>l.debounce?{nextState:"q1",effect:p(c)}:{nextState:"q2",effect:S(l.action),stateUpdater:h}},"q1","debounce("+i(a)+", "+t.name+")")}function m(e,a){for(var t=arguments.length,r=Array(t>2?t-2:0),n=2;t>n;n++)r[n-2]=arguments[n];return f(p,e,a,...r)}function U(e,a){for(var t=arguments.length,r=Array(t>2?t-2:0),n=2;t>n;n++)r[n-2]=arguments[n];return f(S,e,a,...r)}function k(e,a){for(var t=arguments.length,r=Array(t>2?t-2:0),n=2;t>n;n++)r[n-2]=arguments[n];return f(h,e,a,...r)}function b(e,a,t){for(var r=arguments.length,n=Array(r>3?r-3:0),o=3;r>o;o++)n[o-3]=arguments[o];return f(y,e,a,t,...n)}function j(e,a,t){for(var r=arguments.length,n=Array(r>3?r-3:0),f=3;r>f;f++)n[f-3]=arguments[f];return s(g,e,a,t,...n)}function L(e,a,t){for(var r=arguments.length,n=Array(r>3?r-3:0),o=3;r>o;o++)n[o-3]=arguments[o];return f(A,e,a,t,...n)}export{L as debounce,j as retry,m as takeEvery,U as takeLatest,k as takeLeading,b as throttle};
