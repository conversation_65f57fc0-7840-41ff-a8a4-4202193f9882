var a=a=>"@@redux-saga/"+a,r=a("CANCEL_PROMISE"),n=a("CHANNEL_END"),t=a("IO"),e=a("MATCH"),o=a("MULTICAST"),s=a("SAGA_ACTION"),u=a("SELF_CANCELLATION"),c=a("TASK"),i=a("TASK_CANCEL"),l=a("TERMINATE"),f=a("LOCATION");function v(){return(v=Object.assign||function(a){for(var r=1;arguments.length>r;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(a[t]=n[t])}return a}).apply(this,arguments)}var p=a=>null==a,y=a=>null!=a,A=a=>"function"==typeof a,E=a=>"string"==typeof a,C=Array.isArray,d=a=>a&&A(a.then),h=a=>a&&A(a.next)&&A(a.throw),g=a=>a&&(E(a)||L(a)||A(a)||C(a)&&a.every(g)),T=a=>a&&A(a.take)&&A(a.close),O=a=>A(a)&&a.hasOwnProperty("toString"),L=a=>!!a&&"function"==typeof Symbol&&a.constructor===Symbol&&a!==Symbol.prototype,m=a=>T(a)&&a[o],N=(a=>()=>a)(!0),b=()=>{},S=a=>a,_=(a,r)=>{v(a,r),Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(r).forEach(n=>{a[n]=r[n]})},w=(a,r)=>[].concat(...r.map(a));function I(a,r){var n=a.indexOf(r);0>n||a.splice(n,1)}function k(a){var r=!1;return()=>{r||(r=!0,a())}}var P=a=>{throw a},j=a=>({value:a,done:!0});function x(a,r,n){void 0===r&&(r=P),void 0===n&&(n="iterator");var t={meta:{name:n},next:a,throw:r,return:j,isSagaIterator:!0};return"undefined"!=typeof Symbol&&(t[Symbol.iterator]=(()=>t)),t}function H(a,r){var{sagaStack:n}=r;console.error(a),console.error(n)}var K=a=>Array.apply(null,Array(a)),R=a=>r=>a(Object.defineProperty(r,s,{value:!0})),F=a=>a===l,M=a=>a===i,U=a=>F(a)||M(a);function X(a,r){var n,t=Object.keys(a),e=t.length,o=0,s=C(a)?K(e):{},u={};return t.forEach(a=>{var t=(t,u)=>{n||(u||U(t)?(r.cancel(),r(t,u)):(s[a]=t,++o===e&&(n=!0,r(s))))};t.cancel=b,u[a]=t}),r.cancel=(()=>{n||(n=!0,t.forEach(a=>u[a].cancel()))}),u}function D(a){return{name:a.name||"anonymous",location:G(a)}}function G(a){return a[f]}function z(){for(var a=arguments.length,r=Array(a),n=0;a>n;n++)r[n]=arguments[n];return 0===r.length?a=>a:1===r.length?r[0]:r.reduce((a,r)=>(function(){return a(r(...arguments))}))}var J="Channel's Buffer overflow!",q=1,B=3,Q=4,V={isEmpty:N,put:b,take:b};function W(a,r){void 0===a&&(a=10);var n=Array(a),t=0,e=0,o=0,s=r=>{n[e]=r,e=(e+1)%a,t++},u=()=>{if(0!=t){var r=n[o];return n[o]=null,t--,o=(o+1)%a,r}},c=()=>{for(var a=[];t;)a.push(u());return a};return{isEmpty:()=>0==t,put:u=>{var i;if(a>t)s(u);else switch(r){case q:throw Error(J);case B:n[e]=u,o=e=(e+1)%a;break;case Q:i=2*a,n=c(),t=n.length,e=n.length,o=0,n.length=i,a=i,s(u)}},take:u,flush:c}}var Y=()=>V,Z=a=>W(a,B),$=a=>W(a,Q),aa=Object.freeze({__proto__:null,none:Y,fixed:a=>W(a,q),dropping:a=>W(a,2),sliding:Z,expanding:$}),ra="TAKE",na="PUT",ta="ALL",ea="RACE",oa="CALL",sa="CPS",ua="FORK",ca="JOIN",ia="CANCEL",la="SELECT",fa="ACTION_CHANNEL",va="CANCELLED",pa="FLUSH",ya="GET_CONTEXT",Aa="SET_CONTEXT",Ea=Object.freeze({__proto__:null,TAKE:ra,PUT:na,ALL:ta,RACE:ea,CALL:oa,CPS:sa,FORK:ua,JOIN:ca,CANCEL:ia,SELECT:la,ACTION_CHANNEL:fa,CANCELLED:va,FLUSH:pa,GET_CONTEXT:ya,SET_CONTEXT:Aa}),Ca=2147483647;function da(a,n){var t;void 0===n&&(n=!0);var e=new Promise(r=>{t=setTimeout(r,Math.min(Ca,a),n)});return e[r]=(()=>{clearTimeout(t)}),e}var ha=(a,r)=>({[t]:!0,combinator:!1,type:a,payload:r}),ga=a=>ha(ua,v({},a.payload,{detached:!0}));function Ta(a,r){return void 0===a&&(a="*"),g(a)?(y(r)&&console.warn("take(pattern) takes one argument but two were provided. Consider passing an array for listening to several action types"),ha(ra,{pattern:a})):m(a)&&y(r)&&g(r)?ha(ra,{channel:a,pattern:r}):T(a)?(y(r)&&console.warn("take(channel) takes one argument but two were provided. Second argument is ignored."),ha(ra,{channel:a})):void 0}var Oa=function(){var a=Ta(...arguments);return a.payload.maybe=!0,a};function La(a,r){return p(r)&&(r=a,a=void 0),ha(na,{channel:a,action:r})}var ma=function(){var a=La(...arguments);return a.payload.resolve=!0,a};function Na(a){var r=ha(ta,a);return r.combinator=!0,r}function ba(a){var r=ha(ea,a);return r.combinator=!0,r}function Sa(a,r){var n,t=null;return A(a)?n=a:(C(a)?[t,n]=a:({context:t,fn:n}=a),t&&E(n)&&A(t[n])&&(n=t[n])),{context:t,fn:n,args:r}}function _a(a){for(var r=arguments.length,n=Array(r>1?r-1:0),t=1;r>t;t++)n[t-1]=arguments[t];return ha(oa,Sa(a,n))}function wa(a,r,n){return void 0===n&&(n=[]),ha(oa,Sa([a,r],n))}function Ia(a){for(var r=arguments.length,n=Array(r>1?r-1:0),t=1;r>t;t++)n[t-1]=arguments[t];return ha(sa,Sa(a,n))}function ka(a){for(var r=arguments.length,n=Array(r>1?r-1:0),t=1;r>t;t++)n[t-1]=arguments[t];return ha(ua,Sa(a,n))}function Pa(a){for(var r=arguments.length,n=Array(r>1?r-1:0),t=1;r>t;t++)n[t-1]=arguments[t];return ga(ka(a,...n))}function ja(a){return ha(ca,a)}function xa(a){return void 0===a&&(a=u),ha(ia,a)}function Ha(a){void 0===a&&(a=S);for(var r=arguments.length,n=Array(r>1?r-1:0),t=1;r>t;t++)n[t-1]=arguments[t];return ha(la,{selector:a,args:n})}function Ka(a,r){return ha(fa,{pattern:a,buffer:r})}function Ra(){return ha(va,{})}function Fa(a){return ha(pa,a)}function Ma(a){return ha(ya,a)}function Ua(a){return ha(Aa,a)}var Xa=_a.bind(null,da);export{S as $,ta as A,u as B,n as C,K as D,_ as E,ua as F,ya as G,x as H,l as I,ca as J,U as K,b as L,o as M,w as N,G as O,na as P,c as Q,ea as R,s as S,ra as T,i as U,M as V,F as W,t as X,H as Y,z as Z,R as _,C as a,v as a0,aa as a1,f as a2,ga as a3,T as a4,Ta as a5,ka as a6,xa as a7,_a as a8,Xa as a9,Ka as aa,Z as ab,ba as ac,Ea as ad,Oa as ae,La as af,ma as ag,Na as ah,wa as ai,Ia as aj,Pa as ak,ja as al,Ha as am,Ra as an,Fa as ao,Ma as ap,Ua as aq,O as b,L as c,e as d,$ as e,A as f,r as g,oa as h,sa as i,ia as j,N as k,la as l,fa as m,Y as n,k as o,va as p,pa as q,I as r,E as s,Aa as t,d as u,y as v,h as w,D as x,p as y,X as z};
