import{k as e,s as t,a,b as n,f as r,c,C as o,e as s,o as i,M as f,d as u,r as l,n as v,S as d,g as h,T as p,P as m,A as k,R as y,h as E,i as x,F as b,J as j,j as R,l as S,m as M,p as T,q as A,G as C,t as w,u as O,v as N,w as I,x as D,y as q,z,B as L,D as P,E as G,H as _,I as B,K as F,L as H,N as J,O as K,Q,U,V,W,X,Y,Z,_ as $,$ as ee,a0 as te}from"./io-1d900520.js";export{g as CANCEL,a2 as SAGA_LOCATION,a1 as buffers,a3 as detach}from"./io-1d900520.js";var ae=[],ne=0;function re(e){try{se(),e()}finally{ie()}}function ce(e){ae.push(e),ne||(se(),fe())}function oe(e){try{return se(),e()}finally{fe()}}function se(){ne++}function ie(){ne--}function fe(){var e;for(ie();!ne&&void 0!==(e=ae.shift());)re(e)}var ue=e=>t=>e.some(e=>ge(e)(t)),le=e=>t=>e(t),ve=e=>t=>t.type===e+"",de=e=>t=>t.type===e,he=()=>e;function ge(e){var o="*"===e?he:t(e)?ve:a(e)?ue:n(e)?ve:r(e)?le:c(e)?de:null;if(null===o)throw Error("invalid pattern: "+e);return o(e)}var pe={type:o},me=e=>e&&e.type===o;function ke(e){void 0===e&&(e=s());var t=!1,a=[];return{take:function(n){t&&e.isEmpty()?n(pe):e.isEmpty()?(a.push(n),n.cancel=(()=>{l(a,n)})):n(e.take())},put:function(n){if(!t){if(0===a.length)return e.put(n);a.shift()(n)}},flush:function(a){t&&e.isEmpty()?a(pe):a(e.flush())},close:function(){if(!t){t=!0;var e=a;a=[];for(var n=0,r=e.length;r>n;n++)(0,e[n])(pe)}}}}function ye(e,t){void 0===t&&(t=v());var a,n=!1,c=ke(t),o=()=>{n||(n=!0,r(a)&&a(),c.close())};return a=e(e=>{me(e)?o():c.put(e)}),a=i(a),n&&a(),{take:c.take,flush:c.flush,close:o}}function Ee(){var e=!1,t=[],a=t,n=()=>{a===t&&(a=t.slice())},r=()=>{e=!0;var n=t=a;a=[],n.forEach(e=>{e(pe)})};return{[f]:!0,put(n){if(!e)if(me(n))r();else for(var c=t=a,o=0,s=c.length;s>o;o++){var i=c[o];i[u](n)&&(i.cancel(),i(n))}},take(t,r){void 0===r&&(r=he),e?t(pe):(t[u]=r,n(),a.push(t),t.cancel=i(()=>{n(),l(a,t)}))},close:r}}function xe(){var e=Ee(),{put:t}=e;return e.put=(e=>{e[d]?t(e):ce(()=>{t(e)})}),e}var be=0,je=1,Re=2,Se=3;function Me(e,t){var a=e[h];r(a)&&(t.cancel=a),e.then(t,e=>{t(e,!0)})}var Te=0,Ae=()=>++Te;function Ce(e){e.isRunning()&&e.cancel()}var we={[p]:function(e,t,a){var{channel:n=e.channel,pattern:r,maybe:c}=t,o=e=>{e instanceof Error?a(e,!0):!me(e)||c?a(e):a(B)};try{n.take(o,N(r)?ge(r):null)}catch(e){return void a(e,!0)}a.cancel=o.cancel},[m]:function(e,t,a){var{channel:n,action:r,resolve:c}=t;ce(()=>{var t;try{t=(n?n.put:e.dispatch)(r)}catch(e){return void a(e,!0)}c&&O(t)?Me(t,a):a(t)})},[k]:function(e,t,n,r){var{digestEffect:c}=r,o=Te,s=Object.keys(t);if(0!==s.length){var i=z(t,n);s.forEach(e=>{c(t[e],o,i[e],e)})}else n(a(t)?[]:{})},[y]:function(e,t,n,r){var{digestEffect:c}=r,o=Te,s=Object.keys(t),i=a(t)?P(s.length):{},f={},u=!1;s.forEach(e=>{var t=(t,a)=>{u||(a||F(t)?(n.cancel(),n(t,a)):(n.cancel(),u=!0,i[e]=t,n(i)))};t.cancel=H,f[e]=t}),n.cancel=(()=>{u||(u=!0,s.forEach(e=>f[e].cancel()))}),s.forEach(e=>{u||c(t[e],o,f[e],e)})},[E]:function(e,t,a,n){var{context:r,fn:c,args:o}=t,{task:s}=n;try{var i=c.apply(r,o);if(O(i))return void Me(i,a);if(I(i))return void Be(e,i,s.context,Te,D(c),!1,a);a(i)}catch(e){a(e,!0)}},[x]:function(e,t,a){var{context:n,fn:r,args:c}=t;try{var o=(e,t)=>{q(e)?a(t):a(e,!0)};r.apply(n,c.concat(o)),o.cancel&&(a.cancel=o.cancel)}catch(e){a(e,!0)}},[b]:function(e,t,a,n){var{context:r,fn:c,args:o,detached:s}=t,{task:i}=n,f=function(e){var{context:t,fn:a,args:n}=e;try{var r=a.apply(t,n);if(I(r))return r;var c=!1;return _(e=>c?{value:e,done:!0}:(c=!0,{value:r,done:!O(r)}))}catch(e){return _(()=>{throw e})}}({context:r,fn:c,args:o}),u=function(e,t){return e.isSagaIterator?{name:e.meta.name}:D(t)}(f,c);oe(()=>{var t=Be(e,f,i.context,Te,u,s,void 0);s?a(t):t.isRunning()?(i.queue.addTask(t),a(t)):t.isAborted()?i.queue.abort(t.error()):a(t)})},[j]:function(e,t,n,r){var{task:c}=r,o=(e,t)=>{if(e.isRunning()){var a={task:c,cb:t};t.cancel=(()=>{e.isRunning()&&l(e.joiners,a)}),e.joiners.push(a)}else e.isAborted()?t(e.error(),!0):t(e.result())};if(a(t)){if(0===t.length)return void n([]);var s=z(t,n);t.forEach((e,t)=>{o(e,s[t])})}else o(t,n)},[R]:function(e,t,n,r){var{task:c}=r;t===L?Ce(c):a(t)?t.forEach(Ce):Ce(t),n()},[S]:function(e,t,a){var{selector:n,args:r}=t;try{a(n(e.getState(),...r))}catch(e){a(e,!0)}},[M]:function(e,t,a){var{pattern:n,buffer:r}=t,c=ke(r),o=ge(n),s=t=>{me(t)||e.channel.take(s,o),c.put(t)},{close:i}=c;c.close=(()=>{s.cancel(),i()}),e.channel.take(s,o),a(c)},[T]:function(e,t,a,n){var{task:r}=n;a(r.isCancelled())},[A]:function(e,t,a){t.flush(a)},[C]:function(e,t,a,n){var{task:r}=n;a(r.context[t])},[w]:function(e,t,a,n){var{task:r}=n;G(r.context,t),a()}};function Oe(e,t){return e+"?"+t}function Ne(e){var{name:t,location:a}=e;return a?t+"  "+Oe(a.fileName,a.lineNumber):t}function Ie(e){var t=J(e=>e.cancelledTasks,e);return t.length?["Tasks cancelled due to error:",...t].join("\n"):""}var De=null,qe=[],ze=e=>{e.crashedEffect=De,qe.push(e)},Le=()=>{De=null,qe.length=0},Pe=e=>{De=e},Ge=()=>{var[e,...t]=qe,a=e.crashedEffect?function(e){var t=K(e);if(t){var{code:a,fileName:n,lineNumber:r}=t;return a+"  "+Oe(n,r)}return""}(e.crashedEffect):null;return["The above error occurred in task "+Ne(e.meta)+(a?" \n when executing effect "+a:""),...t.map(e=>"    created by "+Ne(e.meta)),Ie(qe)].join("\n")};function _e(e,t,a,n,r,c,o){void 0===o&&(o=H);var s,i,f=be,u=null,v=[],d=Object.create(a),h=function(e,t,a){var n,r=[],c=!1;function o(e){t(),i(),a(e,!0)}function s(t){r.push(t),t.cont=((s,i)=>{c||(l(r,t),t.cont=H,i?o(s):(t===e&&(n=s),r.length||(c=!0,a(n))))})}function i(){c||(c=!0,r.forEach(e=>{e.cont=H,e.cancel()}),r=[])}return s(e),{addTask:s,cancelAll:i,abort:o,getTasks:()=>r}}(t,function(){v.push(...h.getTasks().map(e=>e.meta.name))},g);function g(t,a){if(a){if(f=Re,ze({meta:r,cancelledTasks:v}),p.isRoot){var n=Ge();Le(),e.onError(t,{sagaStack:n})}i=t,u&&u.reject(t)}else t===U?f=je:f!==je&&(f=Se),s=t,u&&u.resolve(t);p.cont(t,a),p.joiners.forEach(e=>{e.cb(t,a)}),p.joiners=null}var p={[Q]:!0,id:n,meta:r,isRoot:c,context:d,joiners:[],queue:h,cancel:function(){f===be&&(f=je,h.cancelAll(),g(U,!1))},cont:o,end:g,setContext:function(e){G(d,e)},toPromise:function(){return u?u.promise:((e={}).promise=new Promise((t,a)=>{e.resolve=t,e.reject=a}),u=e,f===Re?u.reject(i):f!==be&&u.resolve(s),u.promise);var e},isRunning:()=>f===be,isCancelled:()=>f===je||f===be&&t.status===je,isAborted:()=>f===Re,result:()=>s,error:()=>i};return p}function Be(e,t,a,n,c,o,s){var i=e.finalizeRunEffect(function(t,a,n){if(O(t))Me(t,n);else if(I(t))Be(e,t,u.context,a,c,!1,n);else if(t&&t[X]){var r=we[t.type];r(e,t.payload,n,l)}else n(t)});v.cancel=H;var f={meta:c,cancel:function(){f.status===be&&(f.status=je,v(U))},status:be},u=_e(e,f,a,n,c,o,s),l={task:u,digestEffect:d};return s&&(s.cancel=u.cancel),v(),u;function v(e,a){try{var c;a?(c=t.throw(e),Le()):V(e)?(f.status=je,v.cancel(),c=r(t.return)?t.return(U):{done:!0,value:U}):c=W(e)?r(t.return)?t.return():{done:!0}:t.next(e),c.done?(f.status!==je&&(f.status=Se),f.cont(c.value)):d(c.value,n,v)}catch(e){if(f.status===je)throw e;f.status=Re,f.cont(e,!0)}}function d(t,a,n,r){void 0===r&&(r="");var c,o=Ae();function s(a,r){c||(c=!0,n.cancel=H,e.sagaMonitor&&(r?e.sagaMonitor.effectRejected(o,a):e.sagaMonitor.effectResolved(o,a)),r&&Pe(t),n(a,r))}e.sagaMonitor&&e.sagaMonitor.effectTriggered({effectId:o,parentEffectId:a,label:r,effect:t}),s.cancel=H,n.cancel=(()=>{c||(c=!0,s.cancel(),s.cancel=H,e.sagaMonitor&&e.sagaMonitor.effectCancelled(o))}),i(t,o,s)}}function Fe(e,t){for(var{channel:a=xe(),dispatch:n,getState:r,context:c={},sagaMonitor:o,effectMiddlewares:s,onError:i=Y}=e,f=arguments.length,u=Array(f>2?f-2:0),l=2;f>l;l++)u[l-2]=arguments[l];var v,d=t(...u),h=Ae();if(o&&(o.rootSagaStarted=o.rootSagaStarted||H,o.effectTriggered=o.effectTriggered||H,o.effectResolved=o.effectResolved||H,o.effectRejected=o.effectRejected||H,o.effectCancelled=o.effectCancelled||H,o.actionDispatched=o.actionDispatched||H,o.rootSagaStarted({effectId:h,saga:t,args:u})),s){var g=Z(...s);v=(e=>(t,a,n)=>{return g(t=>e(t,a,n))(t)})}else v=ee;var p={channel:a,dispatch:$(n),getState:r,sagaMonitor:o,onError:i,finalizeRunEffect:v};return oe(()=>{var e=Be(p,d,c,h,D(t),!0,void 0);return o&&o.effectResolved(h,e),e})}export default function(e){var t,a=void 0===e?{}:e,{context:n={},channel:r=xe(),sagaMonitor:c}=a,o=function(e,t){if(null==e)return{};var a,n,r={},c=Object.keys(e);for(n=0;c.length>n;n++)0>t.indexOf(a=c[n])&&(r[a]=e[a]);return r}(a,["context","channel","sagaMonitor"]);function s(e){var{getState:a,dispatch:s}=e;return t=Fe.bind(null,te({},o,{context:n,channel:r,dispatch:s,getState:a,sagaMonitor:c})),e=>t=>{c&&c.actionDispatched&&c.actionDispatched(t);var a=e(t);return r.put(t),a}}return s.run=function(){return t(...arguments)},s.setContext=(e=>{G(n,e)}),s}export{pe as END,ke as channel,ye as eventChannel,me as isEnd,Ee as multicastChannel,Fe as runSaga,xe as stdChannel};
