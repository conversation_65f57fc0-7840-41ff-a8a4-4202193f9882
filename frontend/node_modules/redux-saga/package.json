{"name": "redux-saga", "version": "1.3.0", "description": "Saga middleware for Redux to handle Side Effects", "main": "./dist/redux-saga-core-npm-proxy.cjs.js", "module": "./dist/redux-saga-core-npm-proxy.esm.js", "unpkg": "./dist/redux-saga.umd.min.js", "files": ["dist", "effects", "*.d.ts", "import-condition-proxy.mjs"], "exports": {"./effects": {"types": "./effects.d.ts", "module": "./dist/redux-saga-effects-npm-proxy.esm.js", "default": "./dist/redux-saga-effects-npm-proxy.cjs.js"}, ".": {"types": "./index.d.ts", "module": "./dist/redux-saga-core-npm-proxy.esm.js", "import": "./import-condition-proxy.mjs", "default": "./dist/redux-saga-core-npm-proxy.cjs.js"}, "./package.json": "./package.json"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "yarn clean", "build": "rollup -c", "prepare": "yarn build"}, "repository": "https://github.com/redux-saga/redux-saga/tree/main/packages/core", "keywords": ["javascript", "redux", "middleware", "saga", "effects", "side effects"], "author": "Yassine ELOUAFI <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/Andarist)", "<PERSON> <<EMAIL>> (https://github.com/restrry)", "<PERSON> <<EMAIL>> (https://github.com/shinima)", "<PERSON> <<EMAIL> (https://github.com/aikoven)"], "license": "MIT", "bugs": {"url": "https://github.com/redux-saga/redux-saga/issues"}, "homepage": "https://redux-saga.js.org/", "dependencies": {"@redux-saga/core": "^1.3.0"}, "devDependencies": {"@babel/core": "^7.6.4", "@babel/plugin-transform-runtime": "^7.6.2", "@babel/polyfill": "^7.6.0", "@babel/preset-env": "^7.6.3", "lerna-alias": "^3.0.2", "rimraf": "^3.0.0", "rollup": "^1.23.1", "rollup-plugin-alias": "^1.4.0", "rollup-plugin-babel": "5.0.0-alpha.0", "rollup-plugin-node-resolve": "^4.0.0", "rollup-plugin-replace": "^1.1.1", "rollup-plugin-terser": "^4.0.1"}, "types": "./index.d.ts", "npmName": "redux-saga", "npmFileMap": [{"basePath": "/dist/", "files": ["*.js"]}]}