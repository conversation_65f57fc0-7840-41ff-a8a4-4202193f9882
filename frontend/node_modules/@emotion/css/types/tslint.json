{"extends": "@definitelytyped/dtslint/dtslint.json", "rules": {"array-type": [true, "generic"], "import-spacing": false, "file-name-casing": false, "no-default-import": false, "no-null-undefined-union": false, "semicolon": false, "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-module", "check-rest-spread", "check-type", "check-typecast", "check-type-operator", "check-preblock"], "unnecessary-bind": false}}