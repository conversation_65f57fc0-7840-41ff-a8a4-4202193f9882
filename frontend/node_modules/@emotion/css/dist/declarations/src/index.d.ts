export declare const flush: () => void, hydrate: (ids: string[]) => void, cx: (...classNames: import("./create-instance.js").ClassNamesArg[]) => string, merge: (className: string) => string, getRegisteredStyles: (registeredStyles: string[], className: string) => string, injectGlobal: {
    (template: TemplateStringsArray, ...args: import("./create-instance.js").CSSInterpolation[]): void;
    (...args: import("./create-instance.js").CSSInterpolation[]): void;
}, keyframes: {
    (template: TemplateStringsArray, ...args: import("./create-instance.js").CSSInterpolation[]): string;
    (...args: import("./create-instance.js").CSSInterpolation[]): string;
}, css: {
    (template: TemplateStringsArray, ...args: import("./create-instance.js").CSSInterpolation[]): string;
    (...args: import("./create-instance.js").CSSInterpolation[]): string;
}, sheet: import("./create-instance.js").CSSStyleSheet, cache: import("./create-instance.js").EmotionCache;
