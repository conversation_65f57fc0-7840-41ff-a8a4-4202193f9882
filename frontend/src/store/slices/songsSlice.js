import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  songs: [],
  currentSong: null,
  pagination: {
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 10,
  },
  loading: false,
  error: null,
  searchQuery: '',
  sortBy: 'title',
  sortOrder: 'asc',
};

const songsSlice = createSlice({
  name: 'songs',
  initialState,
  reducers: {
    // Fetch songs actions
    fetchSongsRequest: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    fetchSongsSuccess: (state, action) => {
      state.loading = false;
      state.songs = action.payload.songs;
      state.pagination = action.payload.pagination;
      state.error = null;
    },
    fetchSongsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Create song actions
    createSongRequest: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    createSongSuccess: (state, action) => {
      state.loading = false;
      state.songs.unshift(action.payload);
      state.error = null;
    },
    createSongFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Update song actions
    updateSongRequest: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    updateSongSuccess: (state, action) => {
      state.loading = false;
      const index = state.songs.findIndex(song => song.id === action.payload.id);
      if (index !== -1) {
        state.songs[index] = action.payload;
      }
      state.error = null;
    },
    updateSongFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Delete song actions
    deleteSongRequest: (state, action) => {
      state.loading = true;
      state.error = null;
    },
    deleteSongSuccess: (state, action) => {
      state.loading = false;
      state.songs = state.songs.filter(song => song.id !== action.payload);
      state.error = null;
    },
    deleteSongFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // UI state actions
    setCurrentSong: (state, action) => {
      state.currentSong = action.payload;
    },
    clearCurrentSong: (state) => {
      state.currentSong = null;
    },
    setSearchQuery: (state, action) => {
      state.searchQuery = action.payload;
    },
    setSortBy: (state, action) => {
      state.sortBy = action.payload;
    },
    setSortOrder: (state, action) => {
      state.sortOrder = action.payload;
    },
    setCurrentPage: (state, action) => {
      state.pagination.currentPage = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
  },
});

export const {
  fetchSongsRequest,
  fetchSongsSuccess,
  fetchSongsFailure,
  createSongRequest,
  createSongSuccess,
  createSongFailure,
  updateSongRequest,
  updateSongSuccess,
  updateSongFailure,
  deleteSongRequest,
  deleteSongSuccess,
  deleteSongFailure,
  setCurrentSong,
  clearCurrentSong,
  setSearchQuery,
  setSortBy,
  setSortOrder,
  setCurrentPage,
  clearError,
} = songsSlice.actions;

export default songsSlice.reducer;
