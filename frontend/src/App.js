import React from 'react';
import styled from '@emotion/styled';
import SongManagement from './pages/SongManagement';

const AppContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
`;

const Header = styled.header`
  text-align: center;
  margin-bottom: 2rem;
  color: white;
`;

const Title = styled.h1`
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const Subtitle = styled.p`
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 300;
`;

function App() {
  return (
    <AppContainer>
      <Header>
        <Title>🎵 Song Management</Title>
        <Subtitle>Manage your music collection with ease</Subtitle>
      </Header>
      <SongManagement />
    </AppContainer>
  );
}

export default App;
