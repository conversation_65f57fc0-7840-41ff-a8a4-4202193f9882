import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production' 
    ? '/api' 
    : 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers here if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle common errors
    if (error.response?.status === 401) {
      // Handle unauthorized access
      console.error('Unauthorized access');
    } else if (error.response?.status >= 500) {
      // Handle server errors
      console.error('Server error:', error.response.data);
    }
    return Promise.reject(error);
  }
);

// Songs API functions
export const fetchSongs = async ({ page = 1, limit = 10 } = {}) => {
  const response = await api.get('/songs', {
    params: { page, limit },
  });
  return response;
};

export const fetchSongById = async (id) => {
  const response = await api.get(`/songs/${id}`);
  return response;
};

export const createSong = async (songData) => {
  const response = await api.post('/songs', songData);
  return response;
};

export const updateSong = async (id, songData) => {
  const response = await api.put(`/songs/${id}`, songData);
  return response;
};

export const deleteSong = async (id) => {
  const response = await api.delete(`/songs/${id}`);
  return response;
};

// Health check
export const healthCheck = async () => {
  const response = await api.get('/health');
  return response;
};

export default api;
