import React from 'react';
import { useDispatch } from 'react-redux';
import styled from '@emotion/styled';
import SongCard from './SongCard';
import LoadingSpinner from './LoadingSpinner';

const ListContainer = styled.div`
  padding: 1.5rem;
`;

const Grid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
`;

const EmptyIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 1rem;
`;

const EmptyTitle = styled.h3`
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: #495057;
`;

const EmptyDescription = styled.p`
  font-size: 1rem;
  line-height: 1.5;
`;

const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
`;

function SongList({ songs, loading }) {
  if (loading) {
    return (
      <LoadingContainer>
        <LoadingSpinner />
      </LoadingContainer>
    );
  }

  if (!songs || songs.length === 0) {
    return (
      <ListContainer>
        <EmptyState>
          <EmptyIcon>🎵</EmptyIcon>
          <EmptyTitle>No songs found</EmptyTitle>
          <EmptyDescription>
            Start building your music collection by adding your first song!
          </EmptyDescription>
        </EmptyState>
      </ListContainer>
    );
  }

  return (
    <ListContainer>
      <Grid>
        {songs.map((song) => (
          <SongCard key={song.id} song={song} />
        ))}
      </Grid>
    </ListContainer>
  );
}

export default SongList;
