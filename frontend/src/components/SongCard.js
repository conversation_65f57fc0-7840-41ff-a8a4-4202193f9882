import React from 'react';
import { useDispatch } from 'react-redux';
import styled from '@emotion/styled';
import { setCurrentSong } from '../store/slices/songsSlice';
import { openEditModal, openDeleteModal } from '../store/slices/uiSlice';

const Card = styled.div`
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  }
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`;

const SongInfo = styled.div`
  flex: 1;
`;

const Title = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #212529;
  margin-bottom: 0.25rem;
  line-height: 1.3;
`;

const Artist = styled.p`
  font-size: 1rem;
  color: #6c757d;
  margin-bottom: 0.5rem;
`;

const Album = styled.p`
  font-size: 0.9rem;
  color: #868e96;
  font-style: italic;
`;

const Actions = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1.1rem;

  &:hover {
    background: #f8f9fa;
    transform: scale(1.1);
  }

  &.edit {
    color: #0d6efd;
    &:hover {
      background: #e7f1ff;
    }
  }

  &.delete {
    color: #dc3545;
    &:hover {
      background: #f8d7da;
    }
  }
`;

const Details = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e9ecef;
`;

const DetailItem = styled.div`
  display: flex;
  flex-direction: column;
`;

const DetailLabel = styled.span`
  font-size: 0.75rem;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
`;

const DetailValue = styled.span`
  font-size: 0.9rem;
  color: #495057;
  font-weight: 500;
`;

const GenreBadge = styled.span`
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

function SongCard({ song }) {
  const dispatch = useDispatch();

  const handleEdit = () => {
    dispatch(setCurrentSong(song));
    dispatch(openEditModal());
  };

  const handleDelete = () => {
    dispatch(setCurrentSong(song));
    dispatch(openDeleteModal());
  };

  return (
    <Card className="fade-in">
      <CardHeader>
        <SongInfo>
          <Title>{song.title}</Title>
          <Artist>{song.artist}</Artist>
          <Album>{song.album}</Album>
        </SongInfo>
        <Actions>
          <ActionButton className="edit" onClick={handleEdit} title="Edit song">
            ✏️
          </ActionButton>
          <ActionButton className="delete" onClick={handleDelete} title="Delete song">
            🗑️
          </ActionButton>
        </Actions>
      </CardHeader>

      <Details>
        <DetailItem>
          <DetailLabel>Year</DetailLabel>
          <DetailValue>{song.year}</DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Duration</DetailLabel>
          <DetailValue>{song.duration}</DetailValue>
        </DetailItem>
        <DetailItem>
          <DetailLabel>Genre</DetailLabel>
          <DetailValue>
            <GenreBadge>{song.genre}</GenreBadge>
          </DetailValue>
        </DetailItem>
      </Details>
    </Card>
  );
}

export default SongCard;
